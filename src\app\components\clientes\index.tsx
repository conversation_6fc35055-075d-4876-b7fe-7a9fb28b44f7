'use client'
import Clients from './Clients'
import { useState, useEffect } from 'react'
import ModalDeleteClient from '@/app/components/modals/alertModals/ModalDeleteClient'
import Header from '@/app/components/Header'
import { useRouter } from 'next/navigation'
import { AdminClient } from '@/types/admin/types'
import { useAdminStore } from '@/store/admin/useAdminStore'
import { toast } from 'react-toastify'
import LoaderFull from '../loader/LoaderFull'

const HomePage = () => {
  const [openModal, setOpenModal] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const router = useRouter()
  const itemsPerPage = 5

  const { 
    deleteSelectedAdmin, 
    setAdminSelected, 
    fetchAdmins, 
    loading,
    admins,
    adminCount,
    fetchAdminById
  } = useAdminStore()

  const totalPages = Math.ceil(adminCount / itemsPerPage)

  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1)
  }

  const handleNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1)
  }

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
  }

  // Debounce effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 500)

    return () => clearTimeout(timer)
  }, [searchTerm])

  useEffect(() => {
    fetchAdmins({ limit: itemsPerPage, page: currentPage, q: debouncedSearchTerm, orderBy: 'createdAt' })
  }, [currentPage, debouncedSearchTerm, fetchAdmins])

  const handleCloseDeleteClientModal = () => setOpenModal(false)

  const handleViewClient = (client: AdminClient) => {
    setAdminSelected(client)
    const groupId = client.groupId ? client.groupId.toString() : null
    if (!groupId) {
      toast.error('Este cliente no tiene grupo asignado.', {
        position: 'top-right',
        autoClose: 3000,
      })
      return
    }
    router.push(`/clientes/group-client-admin`)
  }

  const handleEditClient = async (client: AdminClient) => {
    await fetchAdminById(client.id)
    router.push('/clientes/edit-client-admin')
  }

  const handleNewClient = () => {
    router.push('/clientes/new-client-admin')
  }

  const handleDeleteClient = (client: AdminClient) => {
    setAdminSelected(client)
    setOpenModal(true)
  }

  const handleConfirmDeleteClient = async () => {
    try {
      await deleteSelectedAdmin()
      await fetchAdmins({ limit: itemsPerPage, page: currentPage, q: debouncedSearchTerm, orderBy: 'createdAt' })
      setOpenModal(false)
      toast.success('Cliente eliminado con éxito', {
        position: 'top-right',
        autoClose: 3000,
      })
    } catch (err) {
      console.error('Error al eliminar el cliente:', err)
      setOpenModal(false)
      toast.error('Hubo un error al eliminar el cliente', {
        position: 'top-right',
        autoClose: 3000,
      })
    }
  }

  return (
    <>
      <Header title="Cuentas de clientes" />
      <Clients
        admins={admins}
        totalPages={totalPages}
        currentPage={currentPage}
        searchTerm={searchTerm}
        onSearch={handleSearch}
        onPrevPage={handlePrevPage}
        onNextPage={handleNextPage}
        onNavigatePage={setCurrentPage}
        onNewClient={handleNewClient}
        onDeleteClient={handleDeleteClient}
        onEditClient={handleEditClient}
        onViewClient={handleViewClient}
      />
      <ModalDeleteClient
        open={openModal}
        onClose={handleCloseDeleteClientModal}
        onConfirm={handleConfirmDeleteClient}
      />
      {loading && <LoaderFull />}
    </>
  )
}

export default HomePage
