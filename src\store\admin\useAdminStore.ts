/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand'
import * as adminApi from '@/api/endpoints/admin'
import {
  AdminData,
  AdminClient,
  AdminBasic,
  AdminDetail,
  AdminResponse,
  AdminByUserIdClient,
  AdminAmount,
} from '@/types/admin/types'

interface AdminState {
  admins: AdminClient[]
  adminSelected: AdminDetail | null
  adminClientSelected: AdminClient | null
  basicAdmins: AdminBasic[]
  amount: number
  loading: boolean
  error: string | null
  adminCount: number
  setAdminSelected: (admin: AdminClient) => void
  deleteSelectedAdmin: () => Promise<void>
  adminsByUserId: AdminByUserIdClient[]
  adminsByGroupId: AdminClient[]
  adminCountByGroupId: number
  amountTransfer: number
  totalActiveCards: number

  fetchAdmins: (params: any) => Promise<void>
  fetchAdminById: (id: string) => Promise<void>
  fetchAdminByRFC: (rfc: string) => Promise<AdminResponse | null>
  createAdmin: (data: Partial<AdminData>) => Promise<AdminResponse | null>
  updateAdmin: (id: string, data: Partial<AdminData>) => Promise<AdminResponse | null>
  deleteAdmin: (id: string) => Promise<void>
  fetchBasicAdmins: () => Promise<void>
  fetchAmount: (params: AdminAmount) => Promise<void>
  clearSelected: () => void
  fetchAdminsByUserId: (params: any) => Promise<void>
  fetchClientAdminByGroupId: (params: any) => Promise<void>
  fetchAmountTransfer: () => Promise<void>
  fetchTotalActiveCards: (id: string | null) => Promise<void>
}

export const useAdminStore = create<AdminState>()((set, get) => ({
  admins: [],
  adminSelected: null,
  adminClientSelected: null,
  basicAdmins: [],
  amount: 0,
  adminCount: 0,
  loading: false,
  error: null,
  adminsByUserId: [],
  adminsByGroupId: [],
  adminCountByGroupId: 0,
  amountTransfer: 0,
  totalActiveCards: 0,

  fetchAdmins: async params => {
    set({ loading: true, error: null })
    try {
      const { admins, count } = await adminApi.getAdmins(params)
      set({ admins, adminCount: count })
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al obtener administradores' })
    } finally {
      set({ loading: false })
    }
  },

  fetchAdminById: async id => {
    set({ loading: true, error: null })
    try {
      set({ adminSelected: null })
      const data = await adminApi.getAdminById(id)
      set({ adminSelected: data })
    } catch (err: any) {
      set({ adminSelected: null })
      set({ error: err?.response?.data?.message || 'Error al obtener admin' })
    } finally {
      set({ loading: false })
    }
  },

  fetchAdminByRFC: async rfc => {
    set({ loading: true, error: null })
    try {
      const data = await adminApi.getAdminByRFC(rfc)
      return data
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al obtener admin' })
      throw err
    } finally {
      set({ loading: false })
    }
  },

  createAdmin: async data => {
    set({ loading: true, error: null })
    try {
      const response = await adminApi.createAdmin(data)
      return response
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al crear admin' })
      throw err
    } finally {
      set({ loading: false })
    }
  },

  updateAdmin: async (id, data) => {
    set({ loading: true, error: null })
    try {
      const response = await adminApi.updateAdmin(id, data)
      return response
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al actualizar admin' })
      throw err
    } finally {
      set({ loading: false })
    }
  },

  deleteAdmin: async id => {
    set({ loading: true, error: null })
    try {
      await adminApi.deleteAdmin(id)
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al eliminar admin' })
    } finally {
      set({ loading: false })
    }
  },

  fetchBasicAdmins: async () => {
    try {
      const data = await adminApi.getBasicAdmins()
      set({ basicAdmins: data })
    } catch {
      set({ basicAdmins: [] })
    }
  },

  fetchAmount: async (params: AdminAmount) => {
    try {
      const data = await adminApi.getAdminAmount(params)
      set({ amount: data.amount })
    } catch {
      set({ amount: 0 })
    }
  },

  clearSelected: () => set({ adminSelected: null }),

  setAdminSelected: admin => set({ adminClientSelected: admin }),

  deleteSelectedAdmin: async () => {
    const { adminClientSelected, admins, deleteAdmin } = get()
    if (!adminClientSelected) return

    try {
      await deleteAdmin(adminClientSelected.id)
      set({
        admins: admins.filter(a => a.id !== adminClientSelected.id),
        adminSelected: null,
      })
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al eliminar admin' })
    }
  },

  fetchAdminsByUserId: async params => {
    set({ loading: true, error: null })
    try {
      const { admins } = await adminApi.getAdminsByUserId(params)
      const sortedAdmins = admins.sort((a, b) =>
        a.company_name.localeCompare(b.company_name, 'es', { sensitivity: 'base' })
      )
      set({ adminsByUserId: sortedAdmins })
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al obtener admins por usuario' })
    } finally {
      set({ loading: false })
    }
  },

  fetchClientAdminByGroupId: async params => {
    set({ loading: true, error: null })
    try {
      set({ adminsByGroupId: [] }) // limpiar antes de la llamada
      const { admins, count } = await adminApi.getAdmins(params)
      set({ adminsByGroupId: Array.isArray(admins) && admins.length > 0 ? admins : [], adminCountByGroupId: count })
    } catch (err: any) {
      set({ adminsByGroupId: [] }) // limpiar también en caso de error
      set({ error: err?.response?.data?.message || 'Error al obtener admins por grupo' })
    } finally {
      set({ loading: false })
    }
  },

  fetchAmountTransfer: async () => {
    try {
      const data = await adminApi.getAmountTransfer()
      set({ amountTransfer: data.availableBalance })
    } catch {
      set({ amountTransfer: 0 })
    }
  },

  fetchTotalActiveCards: async (id: string | null) => {
    try {
      const data = await adminApi.getTotalActiveCards(id)
      set({ totalActiveCards: data.totalActiveCards })
    } catch {
      set({ totalActiveCards: 0 })
    }
  },
}))
