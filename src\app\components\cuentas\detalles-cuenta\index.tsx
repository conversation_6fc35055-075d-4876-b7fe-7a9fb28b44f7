'use client'

import { Suspense, useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { FaArrowLeft } from 'react-icons/fa'
import { toast } from 'react-toastify'

import Header from '@/app/components/Header'
import Button from '../../Button'
import AccountDetails from './AccountDetails'
import ModalCardLock from '../../modals/alertModals/ModalCardLock'
import ModalDeleteCard from '../../modals/alertModals/ModalDeleteCard'
import ModalCardReassignment from '../../modals/alertModals/ModalCardReassignment'
import ModalReassingCardSucces from '../../modals/alertModals/ModalReassingCardSucces'

import { useParams } from 'next/navigation'

import styles from '../../styles/AccountsDetails.module.css'
import { blockCard } from '@/api/endpoints/account'
import { useAccountStore } from '@/store/account/useAccountStore'
import { useCardsStore } from '@/store/cards/useCardsStore'
import { CardStatus } from '@/types/cards/types'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { ROLES } from '@/constants/roles'
import { generateStatementPDFFromTransactions } from '@/utils/generateStatementPDFFromHTML'
import { useListTransactions } from '@/hooks/useListTransactions'

const AccountDetailsPage = () => (
  <Suspense fallback={<p>Cargando...</p>}>
    <Content />
  </Suspense>
)

const Content = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { id: accountId } = useParams<{ id: string }>()

  const admin = searchParams.get('admin') || ''
  const { getUserRoleName } = useAuthStore()
  const {
    fetchCardsByAccount,
    cards,
    updateCardStatusLocally,
    reassignCard,
    deleteCard,
    eliminatedCardResponse,
    cardSelection,
    setCardSelection,
    resetCardSelection,
    modals,
    setModal,
  } = useCardsStore()

  const { fetchAccountById, accountSelected, accountDetails, error } = useAccountStore()

  // Obtener transacciones usando el hook
  const { transactions, isLoading } = useListTransactions(accountSelected?.email || '')

  const [isReassigning, setIsReassigning] = useState(false)
  const [isDownloadingPDF, setIsDownloadingPDF] = useState(false)

  const showToastError = (msg: string) =>
    toast.error(msg, {
      position: 'top-right',
      autoClose: 3000,
      pauseOnHover: true,
      draggable: true,
    })

  const handleToggleCard = (id: string, status: string, last4: string) => {
    setCardSelection({ card: { id, status, last4 } })
    setModal('lockCard', true)
  }

  const confirmToggleCard = async () => {
    const card = cardSelection.card
    if (!card) return
    try {
      const newStatus = card.status === 'NORMAL' ? 'BLOCKED' : 'NORMAL'
      const res = await blockCard({ card_dock_id: card.id, card_status: newStatus })
      updateCardStatusLocally(card.id, res.card_status)
    } catch {
      showToastError('Error al bloquear/desbloquear la tarjeta.')
    } finally {
      setModal('lockCard', false)
      resetCardSelection()
    }
  }

  const confirmDeleteCard = async () => {
    const id = cardSelection.deleteId
    if (!id) return
    await deleteCard({ card_dock_id: id, card_status: CardStatus.CANCELED })

    if (eliminatedCardResponse!.statusCode > 201) {
      showToastError('Error al eliminar la tarjeta')
      return
    }

    setModal('deleteCard', false)
    resetCardSelection()
  }

  const confirmReassignCard = async () => {
    const { reassignId, reassignPan, reason } = cardSelection
    if (!reassignId || !reassignPan) return

    if (!/^\d{16}$/.test(reassignPan)) {
      return setCardSelection({ error: 'El número debe tener exactamente 16 dígitos numéricos.' })
    }

    if (cards.some(c => c.card.id === reassignPan)) {
      return setCardSelection({ error: 'Esta tarjeta ya está asignada a otro usuario.' })
    }

    setIsReassigning(true)
    try {
      const res = await reassignCard({
        email: accountSelected!.email,
        oldCardId: reassignId,
        pan: reassignPan,
        reason,
      })

      if (res?.code !== 200) {
        setCardSelection({ error: res.message || 'Error al reasignar la tarjeta' })
        showToastError('Error al reasignar la tarjeta')
        return
      }

      await fetchCardsByAccount(accountId!)
      setModal('reassignCard', false)
      setModal('successReassign', true)
      resetCardSelection()
    } catch {
      showToastError('Error al reasignar la tarjeta')
    } finally {
      setIsReassigning(false)
    }
  }

  const handleDirectDownload = async () => {
    try {
      if (!accountDetails) {
        showToastError('No se pudo obtener la información de la cuenta')
        return
      }

      if (!transactions || transactions.length === 0) {
        showToastError('No hay movimientos disponibles para generar el estado de cuenta')
        return
      }

      // Mostrar loader
      setIsDownloadingPDF(true)

      // Preparar información de la cuenta
      const accountInfo = {
        name: accountDetails.name || 'N/A',
        convenia_account: accountDetails.convenia_account || 'N/A',
        accountNumberTransfer: accountDetails.accountNumberTransfer || 'N/A',
        admin: {
          companyName: accountSelected?.admin?.companyName || accountDetails.name || 'N/A',
          rfc: accountSelected?.admin?.rfc || 'N/A',
        },
      }

      // Generar PDF usando los datos locales de transacciones
      await generateStatementPDFFromTransactions(
        accountInfo,
        transactions,
        true // usar descarga automática
      )

      // Mostrar toast de éxito en lugar del modal
      toast.success('Estado de cuenta descargado exitosamente', {
        position: 'top-right',
        autoClose: 3000,
        pauseOnHover: true,
        draggable: true,
      })
    } catch (error) {
      console.error('Error al generar el estado de cuenta PDF:', error)
      showToastError('Error al generar el estado de cuenta PDF. Inténtalo de nuevo.')
    } finally {
      // Ocultar loader
      setIsDownloadingPDF(false)
    }
  }

  const handleBackButton = () => (
    <button className={styles.backButton} onClick={() => router.push('/cuentas')}>
      <FaArrowLeft size={16} className={styles.icon} /> Atrás
    </button>
  )

  useEffect(() => {
    if (!accountId) return
    fetchAccountById(accountId)
    fetchCardsByAccount(accountId)
  }, [accountId, fetchAccountById, fetchCardsByAccount, accountSelected?.id])

  const isAdmin = JSON.parse(admin)
  const title = isAdmin ? accountSelected?.admin.companyName || '' : accountDetails?.name || ''
  const userName = isAdmin
    ? `Representante: ${accountDetails?.name || ''}`
    : `Afiliado a ${accountSelected?.admin.companyName || ''}`

  if (error || !accountDetails) {
    return (
      <>
        {handleBackButton()}
        <p className={styles.notFoundText}>No se encontró la cuenta</p>
      </>
    )
  }

  return (
    <>
      {handleBackButton()}
      <Header title={title} userName={userName} />

      <div className={styles.accountNumbers}>
        <p className={styles.subtitle}>
          Número de cuenta Convenia: {accountDetails.convenia_account}
        </p>
        <p className={styles.subtitle}>
          Clabe interbancaria: {accountDetails.accountNumberTransfer}
        </p>
      </div>

      {getUserRoleName() !== ROLES.CLIENTE_LECTOR && (
        <div className={styles.buttonContainer}>
          <Button
            text={
              isDownloadingPDF
                ? 'Generando PDF...'
                : isLoading
                  ? 'Cargando movimientos...'
                  : 'Descargar estado de cuenta general'
            }
            onClick={handleDirectDownload}
            disabled={isDownloadingPDF || isLoading}
          />
        </div>
      )}

      <AccountDetails
        email={accountSelected?.email || ''}
        aliasEmpresa={accountSelected?.admin.alias || ''}
        onToggleCard={handleToggleCard}
        onDeleteCard={(_, id) => {
          setCardSelection({ deleteId: id })
          setModal('deleteCard', true)
        }}
        onCardReassignment={card => {
          setCardSelection({ reassignId: card.id })
          setModal('reassignCard', true)
        }}
      />

      <ModalCardLock
        open={modals.lockCard}
        onClose={() => setModal('lockCard', false)}
        cardLast4={cardSelection.card?.last4 || ''}
        status={cardSelection.card?.status || ''}
        onConfirm={confirmToggleCard}
      />

      <ModalDeleteCard
        open={modals.deleteCard}
        onClose={() => {
          setModal('deleteCard', false)
          resetCardSelection()
        }}
        onPressBtn={confirmDeleteCard}
      />

      <ModalCardReassignment
        open={modals.reassignCard}
        onClose={() => {
          setModal('reassignCard', false)
          setCardSelection({ error: '' })
        }}
        value={cardSelection.reassignPan}
        onChange={val => setCardSelection({ reassignPan: val })}
        reason={cardSelection.reason}
        onChangeReason={val => setCardSelection({ reason: val })}
        error={cardSelection.error}
        loading={isReassigning}
        onPressBtn={confirmReassignCard}
      />

      <ModalReassingCardSucces
        open={modals.successReassign}
        onClose={() => setModal('successReassign', false)}
      />
    </>
  )
}

export default AccountDetailsPage
