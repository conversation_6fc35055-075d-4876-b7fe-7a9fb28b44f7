/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'
import styles from '../../styles/NewUserAdmin.module.css'
import { useRouter } from 'next/navigation'
import { FaArrowLeft } from 'react-icons/fa'
import Button from '@/app/components/Button'
import ContactForm from '../components/ContactForm'
import useFormValidation from '@/hooks/useFormValidationUser'
import { UsersEditProps } from '@/types/types'
import { useEffect, useState } from 'react'
import { useUserStore } from '@/store/user/useUserStore'
import { toast } from 'react-toastify'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'
import { getUserUpdateErrorMessage, getDetailedUserUpdateError, DetailedError } from '@/utils/errorHandler'
import DetailedErrorModal from '../../modals/alertModals/DetailedErrorModal'

const EditUserAdmin = ({ onOpenModal, userData, isEditing }: UsersEditProps) => {
  const router = useRouter()
  const [isChanged, setIsChanged] = useState(false)
  const [openErrorModal, setOpenErrorModal] = useState(false)
  const [errorDetails, setErrorDetails] = useState<DetailedError>({ message: '', errors: [], suggestions: [] })
  const { user } = useAuthStore()
  const { selectedAdminIndex } = useAdminUiStore()
  const { updateConveniaUser } = useUserStore()

  const mapClientDataToFormData = (userData: any) => ({
    name: userData?.name || '',
    email: userData?.email || '',
    phoneAdmin: userData?.phone || '',
    profileType: userData?.roles?.[0]?.id || '',
    adminPassword: userData?.password || '',
    confirmPassword: userData?.confirmPassword || '',
  })

  const { formData, errors, handleInputChange, validate, isFormValid } = useFormValidation(
    mapClientDataToFormData(userData),
    isEditing
  )

  const handleBack = () => {
    router.push('/gestion-usuarios')
  }
 
  const admin = user?.relUserRoleAdmins[selectedAdminIndex]?.admin

  const handleNext = async () => {
    if (!validate()) return

    try {
      if (!userData) {
        throw new Error('User data is undefined')
      }
      await updateConveniaUser(userData.id, {
        name: formData.name.trim(),
        email: formData.email.trim(),
        phone: Number(formData.phoneAdmin),
        password: formData.adminPassword?.trim() || null,
        rol: formData.profileType,
        admin: admin ? admin.id : null
      })
      onOpenModal()
    } catch (error: unknown) {
      console.error('Error al actualizar el usuario:', error)

      // Verificar si es un error que requiere mostrar detalles
      const shouldShowDetailedError = error && typeof error === 'object' && 'response' in error
      const axiosError = error as { response?: { status: number } }
      const isDetailedErrorStatus = axiosError.response?.status && [400, 409, 422].includes(axiosError.response.status)

      if (shouldShowDetailedError && isDetailedErrorStatus) {
        // Mostrar modal con errores detallados
        const details = getDetailedUserUpdateError(error)
        setErrorDetails(details)
        setOpenErrorModal(true)
      } else {
        // Mostrar toast para errores simples
        const message = getUserUpdateErrorMessage(error)
        toast.error(message, {
          position: 'top-right',
          autoClose: 4000,
          pauseOnHover: true,
          draggable: true,
        })
      }
    }
  }

  // Función para determinar si el botón debe estar habilitado
  const isButtonEnabled = (): boolean => {
    return isFormValid() && isChanged
  }

  useEffect(() => {
    if (!userData) {
      setIsChanged(false)
      return
    }
    const normalizedClientData = mapClientDataToFormData(userData)
    const hasChanged = Object.keys(formData).some(key => {
      if (['adminPassword', 'confirmPassword'].includes(key)) return false // ignorar contraseñas

      const formValue = formData[key as keyof typeof formData]
      const clientValue = normalizedClientData[key as keyof typeof normalizedClientData]
      return formValue.trim() !== String(clientValue).trim()
    })
    setIsChanged(hasChanged)
  }, [formData, userData])

  return (
    <>
      <div className={styles.container}>
        <button className={styles.backButton} onClick={handleBack}>
          <FaArrowLeft size={16} className={styles.icon} /> Atrás
        </button>
        <ContactForm formData={formData} onChange={handleInputChange} errors={errors} isEditing={isEditing} />
        <div className={styles.buttonContainer}>
          <Button
            text="Guardar cambios"
            onClick={handleNext}
            fullWidth
            disabled={!isButtonEnabled()}
          />
        </div>
      </div>
      <DetailedErrorModal
        open={openErrorModal}
        onClose={() => setOpenErrorModal(false)}
        title="Error al actualizar el usuario"
        mainMessage={errorDetails.message}
        errors={errorDetails.errors}
        suggestions={errorDetails.suggestions}
      />
    </>
  )
}

export default EditUserAdmin
